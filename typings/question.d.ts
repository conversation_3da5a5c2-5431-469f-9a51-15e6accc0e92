declare namespace Question {
  /**
   * 登录模块
   *
   * - single-choice: 单选题
   * - MultipleChoice: 多选题
   * - TrueFalse: 判断题
   * - FillBlank: 填空题
   */
    type QuestionModule = 'SingleChoice' | 'MultipleChoice' | 'TrueFalse' | 'FillBlank'
    // edit 编辑 answer 回答 preview 预览
    type QuestionType = 'edit' | 'answer' | 'preview'
    // 定义流式数据的接口
    interface QuestionData {
      Question: {
        QuestionType: string
        QuestionTypeId: string
        Title: string
        Options: QuestionOption[] | null // 只有选择，判断，多选题有选项
        Answer: string
        Analysis: string
        Chapters: QuestionChapter[] | null // 只有章节出题时才会有数据
        KnowledgePoints: QuestionKnowledgePoints[] | null // 只有知识点出题时才会有数据
      }
    }
    interface QuestionOption {
    /**
     * 选项内容
     */
      Content: string
      /**
       * 选项标识（A、B、C、D）
       */
      Option: string
    }
    interface QuestionChapter {
    /**
     * 章节名称
     */
      ChapterName: string
      /**
       * 章节ID
       */
      Id: string
    }
    interface QuestionKnowledgePoints {
    /**
     * 知识点内容
     */
      Content: string
      /**
       * 知识点ID
       */
      Id: string
      /**
       * 知识点层级
       */
      Level: number
    }

    /**
     * 处理后的题目数据结构
     * 用于提交后端的标准格式
     */
    interface TransformToDoQuestionData {
      /**
       * 答案解析
       */
      Analysis: string
      /**
       * 正确答案
       */
      Answer: string
      /**
       * 关联的章节列表（章节出题时使用）
       */
      Chapters: QuestionChapter[] | null
      /**
       * 关联的知识点列表（知识点出题时使用）
       */
      KnowledgePoints: QuestionKnowledgePoints[] | null
      /**
       * 选项（选择题、多项选择题、判断题需要）
       */
      Options: QuestionOption[] | null
      /**
       * 题型
       */
      QuestionType: string
      /**
       * 题型ID
       */
      QuestionTypeId: string
      /**
       * 题干
       */
      Title: string
    }

}
