/**
 * 题目数据接收转换器
 * 专注于将API数据转换为前端组件数据
 */
import { nanoid } from '@sa/utils'
import { QuestionTypeId } from '@sa/enum'
import { getQuestionComponentName } from './question-types'

export interface TransformToVoQuestionData {
  /** 题目唯一标识符 */
  id: string
  /** 题型文本描述（如"单选题"、"多选题"等） */
  typeText: string
  /** 题型ID（对应后端定义的题型标识） */
  typeId: string
  /** 题目内容/题干 */
  title: string
  /** 对应前端组件名称（用于动态加载组件） */
  componentsName: string
  /**
   * 题目选项（适用于选择题）
   * @property label - 选项显示文本
   * @property value - 选项实际值
   */
  options: Array<{
    label: string
    value: string
  }> | null
  /** 正确答案（格式根据题型不同而变化） */
  correctAnswer: string
  /** 答案解析 */
  analysis: string
  /** 关联的知识点列表 */
  knowledgePoints: Question.QuestionKnowledgePoints[] | null
  /** 章节列表 */
  chapters: Question.QuestionChapter[] | null
  /** 用户答案（单选为string，多选为string[]） */
  userAnswer: string | string[] | null
}
/**
 * 题目数据接收转换器类
 * 负责将API返回的数据转换为前端组件可用的格式
 */
export class QuestionReceiveTransformer {
  /**
   * 主要转换方法：将API数据转换为前端组件数据
   * @param data 原始API数据
   * @returns 转换后的前端组件数据
   */
  public static transform(data: Question.QuestionData): Question.TransformToVoQuestionData {
    const questionTypeId = data.Question.QuestionTypeId

    // 根据题型ID选择不同的处理方法
    switch (questionTypeId) {
      case QuestionTypeId.SINGLE_CHOICE:
        return this.processSingleChoiceData(data)
      case QuestionTypeId.TRUE_FALSE:
        return this.processTrueFalseData(data)
      case QuestionTypeId.MULTIPLE_CHOICE:
        return this.processMultipleChoiceData(data)
      case QuestionTypeId.FILL_BLANK:
        return this.processFillBlankData(data)
      default:
        return this.processSingleChoiceData(data)
    }
  }

  /**
   * 处理单选题数据
   * @param data 原始API数据
   * @returns 处理后的单选题数据
   */
  private static processSingleChoiceData(data: Question.QuestionData): Question.TransformToVoQuestionData {
    const {
      QuestionType: typeText,
      QuestionTypeId: typeId,
      Title: title,
      Options = [],
      Answer: correctAnswer,
      Analysis,
      KnowledgePoints,
      Chapters,
    } = data.Question

    return {
      id: nanoid(),
      componentsName: getQuestionComponentName(typeId),
      typeText,
      typeId,
      title,
      options: Options?.map(option => ({
        label: option.Content,
        value: option.Option,
      })) || null,
      correctAnswer,
      analysis: Analysis,
      knowledgePoints: KnowledgePoints || [],
      chapters: Chapters || [],
      userAnswer: null,
    }
  }

  /**
   * 处理判断题数据
   * @param data 原始API数据
   * @returns 处理后的判断题数据
   */
  private static processTrueFalseData(data: Question.QuestionData): Question.TransformToVoQuestionData {
    // 判断题处理逻辑与单选题类似，但需要特殊处理选项
    const result = this.processSingleChoiceData(data)

    // 确保判断题只有两个选项：正确/错误
    if (!result.options || result.options.length === 0) {
      result.options = [
        { label: '正确', value: 'A' },
        { label: '错误', value: 'B' },
      ]
    }

    return result
  }

  /**
   * 处理多选题数据
   * @param data 原始API数据
   * @returns 处理后的多选题数据
   */
  private static processMultipleChoiceData(data: Question.QuestionData): Question.TransformToVoQuestionData {
    const result = this.processSingleChoiceData(data)

    // 多选题特定处理：初始化用户答案为空数组
    if (typeof result.correctAnswer === 'string' && result.correctAnswer.includes(',')) {
      result.userAnswer = [] // 多选题的用户答案初始化为空数组
    }

    return result
  }

  /**
   * 处理填空题数据
   * @param data 原始API数据
   * @returns 处理后的填空题数据
   */
  private static processFillBlankData(data: Question.QuestionData): Question.TransformToVoQuestionData {
    const result = this.processSingleChoiceData(data)

    // 填空题没有选项
    result.options = null

    return result
  }

  /**
   * 批量转换API数据
   * @param dataList API数据数组
   * @returns 转换后的前端组件数据数组
   */
  public static batchTransform(dataList: Question.QuestionData[]): Question.TransformToVoQuestionData[] {
    if (!Array.isArray(dataList)) {
      throw new TypeError('数据必须是数组格式')
    }

    return dataList.map(data => this.transform(data))
  }
}
