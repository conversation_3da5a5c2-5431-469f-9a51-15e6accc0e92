/**
 * 题型数据处理工具
 * 用于处理流式数据并转换为组件需要的格式
 */

// 导入拆分后的模块
import { QuestionReceiveTransformer } from './question-receive-transformer'
import { QuestionSubmitTransformer } from './question-submit-transformer'
/**
 * 题目数据转换器类（统一入口）
 * 负责处理题目数据的接收转换和提交转换
 */
export class QuestionDataConverter {
  /**
   * 接收转换：将API数据转换为前端组件数据
   * @param data 原始API数据
   * @returns 转换后的前端组件数据
   */
  public static receiveTransform(data: Question.QuestionData): Question.TransformToVoQuestionData {
    return QuestionReceiveTransformer.transform(data)
  }

  /**
   * 提交转换：将前端组件数据转换为API所需格式
   * @param question 前端组件数据
   * @returns 转换后的API数据
   */
  public static submitTransform(question: Question.TransformToVoQuestionData): Question.TransformToDoQuestionData {
    return QuestionSubmitTransformer.transform(question)
  }

  /**
   * 批量接收转换
   * @param dataList API数据数组
   * @returns 转换后的前端组件数据数组
   */
  public static batchReceiveTransform(dataList: Question.QuestionData[]): Question.TransformToVoQuestionData[] {
    return QuestionReceiveTransformer.batchTransform(dataList)
  }

  /**
   * 批量提交转换
   * @param questions 前端题目数据数组
   * @returns 转换后的API格式数据数组
   */
  public static batchSubmitTransform(questions: Question.TransformToVoQuestionData[]): Question.TransformToDoQuestionData[] {
    return QuestionSubmitTransformer.batchTransform(questions)
  }
}
