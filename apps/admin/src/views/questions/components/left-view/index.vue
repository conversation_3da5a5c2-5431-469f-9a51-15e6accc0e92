<script setup lang="ts">
import { computed, onMounted, readonly, ref } from 'vue'
import { useLoading, useNaiveForm, useResetReactive } from '@sa/hooks'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { QuestionDataConverter, getServiceBaseURL } from '@sa/utils'
import SelectDrawer from '../selectDown/index.vue'
import KnowledgePointForm from './components/knowledge-point-form.vue'
import TextForm from './components/text-form.vue'
import AttachmentForm from './components/attachment-form.vue'
import ChapterForm from './components/chapter-form.vue'
import { useAuthStore } from '@/store/modules/auth'
import { getAgentModelInfo, getChapterList, getDifficulty, getLearningLevel, getQuestionTypes } from '@/service/api'

defineOptions({
  name: 'LeftView',
})
// 定义组件的 emits
const emit = defineEmits<{
  questionGenerated: [data: Question.TransformToVoQuestionData]
  generationProgress: [progress: { current: number, total: number, description: string }]
  generationComplete: []
  generationError: [error: string]
  generationStarted: []
}>()
const questionDataConverter = new QuestionDataConverter()
const showModal = ref(false)

// 环境配置
const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y'
const { baseURL } = getServiceBaseURL(import.meta.env, isHttpProxy)
// 获取认证store
const authStore = useAuthStore()
const { token, activeState, yearSemester } = authStore

// 全屏 loading 状态
const { loading: isGenerating, startLoading, endLoading } = useLoading()

// 表单引用和校验
const { formRef, validate } = useNaiveForm()
const chapterOptions = ref<QuestionsApi.GetChapterListResponse[]>([])
// 知识点表单引用
const knowledgePointFormRef = ref()
// 文本表单引用
const textFormRef = ref()
// 附件表单引用
const attachmentFormRef = ref()
// 章节表单引用
const chapterFormRef = ref()
// 创建方式选项
const createMethods = [
  { key: 'knowledgePoint', label: '知识点出题 ', active: true, mode: 1 as const },
  { key: 'text', label: '文本出题', active: false, mode: 2 as const },
  { key: 'attachment', label: '附件出题', active: false, mode: 3 as const },
  { key: 'chapter', label: '章节出题', active: false, mode: 4 as const },
]

// AI模型选择选项
const modelOptions = ref([] as QuestionsApi.AgentModelInfoResponse[])

// 使用 useResetReactive 创建可重置的表单数据
const [formModel, resetFormModel] = useResetReactive<QuestionsApi.CreateQuestionRequest>({
  AdditionalRequirements: '', // 补充内容（出题要求）
  AIModeId: '', // AI模型ID
  ChapterIds: [], // 章节ID
  DifficultyLevelName: null, // 难度等级名称
  FileUrls: [], // 文件URL
  Grade: 1, // 年级
  KnowledgePointIds: [] as string[], // 知识点ID列表
  Mode: 1, // 出题模式
  QuestionCount: null, // 出题数量
  QuestionDirectionName: null, // 出题方向名称
  QuestionTypeIds: [], // 题型ID列表
  TextContent: '', // 文本内容
})

// 表单校验规则
const rules = {
  QuestionTypeIds: [
    {
      required: true,
      message: '请选择题目的题型',
      trigger: ['blur', 'change'],
      validator: (_rule: any, value: any[]) => {
        if (!value || value.length === 0) {
          return new Error('请选择题目的题型')
        }
        return true
      },
    },
  ],
  QuestionCount: [
    {
      required: true,
      message: '请输入出题数量',
      trigger: ['blur', 'change'],
      validator: (_rule: any, value: number) => {
        if (!value || value < 1) {
          return new Error('出题数量至少为1')
        }
        if (value > 30) {
          return new Error('出题数量不能超过30')
        }
        return true
      },
    },
  ],
}

// 处理模型选择
function handleModelSelect(key: string) {
  formModel.AIModeId = key
}
// 处理创建方式切换
function switchCreateMethod(key: 1 | 2 | 3 | 4) {
  // 保存当前的模型ID
  const currentModelId = formModel.AIModeId
  // resetFormModel()
  formModel.Mode = key
  // 恢复模型ID
  formModel.AIModeId = currentModelId
}

async function generateLesson() {
  // 表单校验
  try {
    // 如果是知识点出题模式，还需要校验知识点表单
    if (formModel.Mode === 1 && knowledgePointFormRef.value) {
      await knowledgePointFormRef.value.validate()
    }

    // 如果是文本出题模式，还需要校验文本表单
    if (formModel.Mode === 2 && textFormRef.value) {
      await textFormRef.value.validate()
    }

    // 如果是附件出题模式，还需要校验附件表单
    if (formModel.Mode === 3 && attachmentFormRef.value) {
      await attachmentFormRef.value.validate()
    }

    // 如果是章节出题模式，还需要校验章节表单
    if (formModel.Mode === 4 && chapterFormRef.value) {
      await chapterFormRef.value.validate()
    }

    // 校验主表单（题型选择、题目数量等）
    await validate()
  }
  catch (error) {
    console.error('表单校验失败:', error)
    // 校验失败，不执行后续操作
    return
  }

  // 开始全屏 loading
  startLoading()
  emit('generationStarted')

  fetchEventSource(`${baseURL}/AgentIntelligentQuestion/AgentIntelligentQuestion/GenerateQuestionsStream`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(formModel),
    openWhenHidden: true,
    onopen: async (response) => {
      console.log('onopen', response)
      if (!response.ok) {
        endLoading() // 请求失败时关闭 loading
        throw new Error(`HTTP error! status: ${response.status}`)
      }
    },
    onmessage: async (msg) => {
      try {
        const streamData = JSON.parse(msg.data)
        if (!streamData.Success) {
          endLoading() // 请求失败时关闭 loading
          return window.$message?.error(streamData.Content)
        }
        console.log('接收到流式数据:', streamData)

        // 处理题目数据
        if (streamData.MessageType === 'question' && streamData.Question) {
          const processedData = questionDataConverter.receiveTransform(streamData)
          console.log('processedData', processedData)
          if (processedData) {
            emit('questionGenerated', processedData)
          }
          else {
            console.warn('题目数据处理失败或验证不通过:', streamData)
          }
        }

        // 处理其他类型的消息
        if (streamData.MessageType === 'statistics') {
          endLoading() // 生成完成时关闭 loading
          console.log(formModel)
          emit('generationComplete')
        }

        if (streamData.MessageType === 'error') {
          endLoading() // 生成错误时关闭 loading
          emit('generationError', streamData.Content || '生成题目时发生错误')
        }
      }
      catch (error) {
        console.error('解析SSE消息失败:', error)
        // endLoading() // 解析错误时关闭 loading
        emit('generationError', '解析数据失败')
      }
    },
    onclose: () => {
      console.log('SSE连接已关闭')
      endLoading() // 连接关闭时关闭 loading
      emit('generationComplete')
    },
    onerror: (err) => {
      console.error('SSE连接错误:', err)
      endLoading() // 连接错误时关闭 loading
      emit('generationError', '连接错误')
      throw err
    },
  })
}
const questionTypeOptions = ref([] as QuestionsApi.GetQuestionTypesResponse[])
const difficultyOptions = ref([] as QuestionsApi.GetDifficultyResponse[])
const learningLevelOptions = ref([] as QuestionsApi.GetLearningLevelResponse[])

// 获取所有章节的扁平化数据（包括父级和子级）
const allFlatChapterNodes = computed(() => {
  const nodes: Array<{ key: string, label: string }> = []

  chapterOptions.value.forEach((chapter) => {
    nodes.push({ key: chapter.ChapterId, label: chapter.ChapterName })
    if (chapter.Second) {
      chapter.Second.forEach((child) => {
        nodes.push({ key: child.ChapterId, label: child.ChapterName })
      })
    }
  })

  return nodes
})
function initData() {
  getAgentModelInfo().then((res) => {
    if (res.data) {
      modelOptions.value = res.data
      formModel.AIModeId = res.data[0].Id
    }
  })
  getQuestionTypes().then((res) => {
    if (res.data) {
      questionTypeOptions.value = res.data
    }
  })
  getDifficulty({
    grade: 1,
    year: '2023',
  }).then((res) => {
    if (res.data) {
      difficultyOptions.value = res.data
      console.log('difficultyOptions loaded:', res.data)
    }
  })
  getLearningLevel({
    grade: 1,
    year: '2023',
  }).then((res) => {
    if (res.data) {
      learningLevelOptions.value = res.data
    }
  })
  getChapterList({
    // classid: activeState.classId,
    grade: activeState.grade,
    term: yearSemester.NowTerm!,
    year: yearSemester.NowYear!,
  }).then((res) => {
    if (res.data) {
      chapterOptions.value = res.data
      console.log('chapterOptions loaded:', chapterOptions.value)
    }
  })
}

// 处理章节选择确认
function handleChapterSelect(selectedChapterIds: string[]) {
  formModel.ChapterIds = selectedChapterIds
}

onMounted(() => {
  initData()
})

// 暴露表单数据给父组件
defineExpose({
  formModel: readonly(formModel),
})
</script>

<template>
  <div class="h-full flex flex-col rounded-8px bg-white p-12px">
    <div class="shrink-0">
      <!-- 标题栏 -->
      <div class="mb-16px flex items-center justify-between">
        <div class="flex items-center gap-8px">
          <div class="h-38px w-38px flex items-center justify-center rounded-8px from-blue-500 to-purple-500">
            <SvgIcon icon="mdi:robot-outline" class="text-20px" />
          </div>
          <span class="text-22px font-600">
            AI智能命题
          </span>
        </div>

        <!-- AI模型选择下拉框 -->
        <NDropdown
          trigger="click"
          key-field="Id"
          label-field="ModelName"
          :options="modelOptions"
          :show-arrow="true"
          @select="handleModelSelect"
        >
          <NButton quaternary class="flex items-center gap-6px rounded-8px bg-blue-100 px-12px py-8px hover:bg-blue-200">
            <SvgIcon icon="mdi:brain" class="mr-4px text-16px text-blue-600" />
            <span class="text-14px text-blue-700">{{ formModel.Mode ? modelOptions.find(m => m.Id === formModel.AIModeId)?.ModelName : '选择模型' }}</span>
            <SvgIcon icon="mdi:chevron-down" class="text-12px text-blue-600" />
          </NButton>
        </NDropdown>
      </div>

      <!-- 创建方式选项卡 -->
      <div class="mb-16px flex justify-between rounded-8px bg-blue-100 p-4px">
        <div
          v-for="method in createMethods"
          :key="method.key"
          class="cursor-pointer rounded-6px px-16px py-8px text-14px font-500 transition-all duration-200"
          :class="[
            formModel.Mode === method.mode
              ? 'bg-white text-blue-600 shadow-sm '
              : ' hover:text-blue-600',
          ]"
          @click="switchCreateMethod(method.mode)"
        >
          {{ method.label }}
        </div>
      </div>
    </div>
    <!-- 内容区域 -->
    <div class="min-h-0 flex-1">
      <NScrollbar class="h-full">
        <!-- 根据创建方式渲染不同的表单组件 -->
        <!-- 知识点出题 -->
        <KnowledgePointForm
          v-if="formModel.Mode === 1"
          ref="knowledgePointFormRef"
          v-model:model-info="formModel"
        />
        <!-- 文本出题 -->
        <TextForm
          v-if="formModel.Mode === 2"
          ref="textFormRef"
          v-model:text-content="formModel.TextContent"
        />
        <!-- 附件出题 -->
        <AttachmentForm
          v-if="formModel.Mode === 3"
          ref="attachmentFormRef"
          v-model:model-info="formModel"
        />
        <!-- 章节出题 -->
        <ChapterForm
          v-if="formModel.Mode === 4"
          ref="chapterFormRef"
          v-model:model-info="formModel"
          :chapter-options="chapterOptions"
          :all-flat-nodes="allFlatChapterNodes"
          :on-select-knowledge-point="() => showModal = true"
        />
      </NScrollbar>
    </div>

    <!-- 生成按钮区域 -->
    <div class="shrink-0 rounded-8px bg-blue-50 p-16px">
      <NForm ref="formRef" :model="formModel" :rules="rules">
        <NGrid :cols="2" :x-gap="12">
          <NFormItemGi path="QuestionTypeIds">
            <NSelect
              v-model:value="formModel.QuestionTypeIds"
              label-field="Name"
              value-field="Id"
              :max-tag-count="1"
              clearable
              multiple
              :options="questionTypeOptions"
              placeholder="选择题目的题型"
            />
          </NFormItemGi>

          <NFormItemGi>
            <NSelect
              v-model:value="formModel.DifficultyLevelName"
              label-field="text"
              value-field="text"
              :options="difficultyOptions"
              placeholder="请选择题目难度"
            />
          </NFormItemGi>

          <NFormItemGi>
            <NSelect
              v-model:value="formModel.QuestionDirectionName"
              label-field="Text"
              value-field="Text"
              :options="learningLevelOptions"
              placeholder="请选择题目方向"
              clearable
            />
          </NFormItemGi>

          <NFormItemGi path="QuestionCount">
            <NInputNumber
              v-model:value="formModel.QuestionCount"
              placeholder="请输入出题数量"
              :max="30"
              :min="1"
            />
          </NFormItemGi>
        </NGrid>
      </NForm>

      <div class="flex-col items-center justify-center">
        <NButton
          type="primary"
          size="large"
          :loading="isGenerating"
          :disabled="isGenerating"
          class="mb-12px from-blue-500 to-purple-500 bg-gradient-to-r px-24px py-12px"
          @click="generateLesson"
        >
          <template #icon>
            <SvgIcon icon="mdi:magic-staff" />
          </template>
          立即出题
        </NButton>
        <p class="text-12px text-gray-500">
          内容由AI生成，仅供参考。
        </p>
      </div>
    </div>

    <!-- 全屏 Loading 覆盖层 -->
    <div
      v-if="isGenerating"
      class="fixed inset-0 z-9999 flex items-center justify-center bg-black bg-opacity-50"
    >
      <div class="flex flex-col items-center justify-center rounded-12px bg-white p-32px shadow-lg">
        <NSpin size="large" />
        <div class="mt-16px text-16px text-gray-700 font-500">
          AI正在生成题目，请稍候...
        </div>
        <div class="mt-8px text-12px text-gray-500">
          内容由AI生成，请耐心等待
        </div>
      </div>
    </div>
    <SelectDrawer
      v-model:is-show-modal="showModal"
      :chapter-options="chapterOptions"
      :initial-selected-ids="formModel.ChapterIds"
      title="关联知识点"
      @confirm="handleChapterSelect"
    />
  </div>
</template>

<style scoped lang="scss">

</style>
